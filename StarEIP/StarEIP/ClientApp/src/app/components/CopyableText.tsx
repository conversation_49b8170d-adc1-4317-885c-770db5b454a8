import React, { useState } from 'react';
import { ActionIcon, CopyButton, Tooltip, Box } from '@mantine/core';
import { IconCopy, IconCheck } from '@tabler/icons-react';

interface CopyableTextProps {
  value: string;
  children: React.ReactNode;
  timeout?: number;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

const CopyableText: React.FC<CopyableTextProps> = ({ 
  value, 
  children, 
  timeout = 2000, 
  position = 'right' 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Box
      style={{ 
        display: 'inline-flex', 
        alignItems: 'center', 
        gap: '4px',
        position: 'relative'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <span>{children}</span>
      {isHovered && (
        <CopyButton value={value} timeout={timeout}>
          {({ copied, copy }) => (
            <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position={position}>
              <ActionIcon 
                color={copied ? 'teal' : 'gray'} 
                variant="subtle" 
                onClick={copy}
                size="xs"
              >
                {copied ? <IconCheck size={12} /> : <IconCopy size={12} />}
              </ActionIcon>
            </Tooltip>
          )}
        </CopyButton>
      )}
    </Box>
  );
};

export default CopyableText;
