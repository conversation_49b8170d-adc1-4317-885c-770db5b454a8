import React, { useState } from 'react';
import { ActionIcon, CopyButton, Tooltip, Box } from '@mantine/core';
import { IconCopy, IconCheck } from '@tabler/icons-react';

interface CopyableTextProps {
  value: string;
  children: React.ReactNode;
  timeout?: number;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

const CopyableText: React.FC<CopyableTextProps> = ({
  value,
  children,
  timeout = 2000,
  position = 'right'
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <CopyButton value={value} timeout={timeout}>
      {({ copied, copy }) => (
        <Box
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '4px',
            position: 'relative',
            cursor: 'pointer'
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          onClick={copy}
        >
          <span>{children}</span>
          {isHovered && (
            <Tooltip label={copied ? 'Copied' : 'Copy'} withArrow position={position}>
              <ActionIcon
                color={copied ? 'teal' : 'gray'}
                variant="subtle"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent double-click when clicking the button
                  copy();
                }}
                size="xs"
              >
                {copied ? <IconCheck size={12} /> : <IconCopy size={12} />}
              </ActionIcon>
            </Tooltip>
          )}
        </Box>
      )}
    </CopyButton>
  );
};

export default CopyableText;
